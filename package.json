{"name": "resume-api-server", "version": "1.0.0", "description": "API代理服务器，用于调用昆鹏360接口", "main": "api-server.js", "scripts": {"start": "node api-server.js", "dev": "node start-server.js", "prod": "NODE_ENV=production node api-server.js", "pm2:start": "pm2 start api-server.js --name resume-api", "pm2:stop": "pm2 stop resume-api", "pm2:restart": "pm2 restart resume-api", "pm2:logs": "pm2 logs resume-api"}, "keywords": ["api", "proxy", "server", "kunpeng360"], "author": "", "license": "MIT", "engines": {"node": ">=12.0.0"}, "devDependencies": {}, "dependencies": {}}