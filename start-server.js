#!/usr/bin/env node

/**
 * 启动脚本 - 支持不同环境配置
 * 使用方法：
 * node start-server.js
 * node start-server.js --port 8080
 * node start-server.js --host 0.0.0.0 --port 3000
 */

const { spawn } = require('child_process');
const path = require('path');

// 解析命令行参数
const args = process.argv.slice(2);
const config = {
    port: 3000,
    host: '0.0.0.0'
};

// 解析参数
for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
        case '--port':
        case '-p':
            config.port = args[i + 1];
            i++;
            break;
        case '--host':
        case '-h':
            config.host = args[i + 1];
            i++;
            break;
        case '--help':
            console.log(`
使用方法:
  node start-server.js [选项]

选项:
  --port, -p <端口>     设置服务器端口 (默认: 3000)
  --host, -h <地址>     设置监听地址 (默认: 0.0.0.0)
  --help               显示帮助信息

示例:
  node start-server.js
  node start-server.js --port 8080
  node start-server.js --host localhost --port 3000
            `);
            process.exit(0);
            break;
    }
}

console.log('🚀 启动API服务器...');
console.log(`📍 端口: ${config.port}`);
console.log(`🌐 监听地址: ${config.host}`);
console.log('');

// 设置环境变量
process.env.PORT = config.port;
process.env.HOST = config.host;

// 启动API服务器
const serverPath = path.join(__dirname, 'api-server.js');
const server = spawn('node', [serverPath], {
    stdio: 'inherit',
    env: process.env
});

// 处理进程退出
server.on('close', (code) => {
    console.log(`\n服务器进程退出，退出码: ${code}`);
});

// 处理Ctrl+C
process.on('SIGINT', () => {
    console.log('\n正在关闭服务器...');
    server.kill('SIGINT');
});

process.on('SIGTERM', () => {
    console.log('\n正在关闭服务器...');
    server.kill('SIGTERM');
});
