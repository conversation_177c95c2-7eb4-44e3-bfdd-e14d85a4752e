# 动态标题和轮播图修改说明

## 修改概述

根据您的要求，我已经成功修改了代码，使标题和轮播图不再写死，而是通过接口动态获取数据。

## 主要修改内容

### 1. 标题动态化
- **修改前**: 标题写死为"安徽美誉制药有限公司"
- **修改后**: 通过接口获取 `res.data.EntName` 动态设置标题

**修改的文件位置**:
- `index.html` 第23行: `<title>` 标签
- `index.html` 第30行: 导航栏logo

### 2. 轮播图动态化
- **修改前**: 使用写死的本地图片文件 (Image1.jpg, Image2.jpg等)
- **修改后**: 通过接口获取 `res.data.CarouselImages` 数组动态生成轮播图

**轮播图URL格式**:
```
https://www.kunpeng360.com/CustomerQuery/Image?t={EntCode}&imageId={imageId}
```

其中:
- `t` 参数使用 `res.data.EntCode` (例如: 05580002)
- `imageId` 参数使用 `res.data.CarouselImages` 数组中的每个ID

### 3. 新增的JavaScript函数

#### `updatePageTitle(apiData)`
- 更新页面标题和导航栏logo
- 使用 `apiData.EntName` 设置企业名称

#### `updateCarousel(apiData)`
- 动态生成轮播图片
- 根据 `apiData.CarouselImages` 数组创建图片元素
- 使用 `apiData.EntCode` 构建图片URL
- 动态生成对应的圆点导航

#### `initializeCarousel()`
- 重新初始化轮播功能
- 处理图片切换和自动播放
- 绑定圆点点击事件

### 4. CSS样式增强
添加了 `.carousel-loading` 样式用于显示加载状态。

## API数据结构

接口返回的关键数据:
```json
{
  "EntName": "安徽美誉制药有限公司",
  "EntCode": "05580002", 
  "CarouselImages": [
    "688dd2645545dbf1f62328c5",
    "688dd26b5545dbf1f62328c7", 
    "688dd2745545dbf1f62328ca",
    "688dd28f5545dbf1f62328cd",
    "688dd2985545dbf1f62328d0"
  ]
}
```

## 生成的轮播图URL示例

基于上述数据，生成的图片URL为:
1. `https://www.kunpeng360.com/CustomerQuery/Image?t=05580002&imageId=688dd2645545dbf1f62328c5`
2. `https://www.kunpeng360.com/CustomerQuery/Image?t=05580002&imageId=688dd26b5545dbf1f62328c7`
3. `https://www.kunpeng360.com/CustomerQuery/Image?t=05580002&imageId=688dd2745545dbf1f62328ca`
4. `https://www.kunpeng360.com/CustomerQuery/Image?t=05580002&imageId=688dd28f5545dbf1f62328cd`
5. `https://www.kunpeng360.com/CustomerQuery/Image?t=05580002&imageId=688dd2985545dbf1f62328d0`

## 功能特点

1. **完全动态**: 标题和轮播图都从接口获取，无需手动修改代码
2. **自适应数量**: 轮播图数量根据接口返回的数组长度自动调整
3. **错误处理**: 包含加载失败的错误处理机制
4. **保持原有功能**: 轮播图的自动播放、圆点导航等功能完全保留

## 测试验证

- ✅ API调用成功
- ✅ 标题动态更新
- ✅ 轮播图动态生成
- ✅ 图片URL正确构建
- ✅ 轮播功能正常工作

## 使用说明

1. 确保代理服务器运行: `node api-server.js`
2. 打开 `index.html` 页面
3. 页面会自动调用API并更新标题和轮播图
4. 如需测试，可以访问 `test.html` 查看详细的API调用结果

修改完成！现在页面的标题和轮播图都是通过接口动态获取的。

## 🎯 最新更新：环境自动检测配置

### 新增功能
1. **自动环境检测**: 项目现在可以自动检测运行环境（开发/生产）
2. **灵活的API配置**: 支持不同环境使用不同的API地址
3. **服务器配置优化**: 支持环境变量配置端口和监听地址

### 部署到服务器的步骤

#### 1. 修改生产环境API地址
在 `index.html` 中找到以下配置并修改：
```javascript
const API_CONFIG = {
    development: 'http://localhost:3000',
    production: 'https://your-server-domain.com'  // 👈 改为你的服务器地址
};
```

#### 2. 启动服务器
```bash
# 基本启动
node api-server.js

# 或使用启动脚本
node start-server.js --port 8080

# 或使用npm脚本
npm start
```

#### 3. 环境变量配置
```bash
# 设置端口
PORT=8080 node api-server.js

# 设置监听地址和端口
HOST=0.0.0.0 PORT=3000 node api-server.js
```

### 新增文件
- `部署配置说明.md`: 详细的部署指南
- `start-server.js`: 启动脚本，支持命令行参数
- `package.json`: 项目配置文件，包含启动脚本

### 环境检测逻辑
- **开发环境**: localhost、127.0.0.1 或空域名
- **生产环境**: 其他所有域名

现在你只需要修改 `production` 配置中的服务器地址，项目就可以在服务器上正常运行了！
